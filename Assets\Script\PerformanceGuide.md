# 暗黑2风格游戏 - XLua性能优化指南

## 🎯 核心优化策略

### 1. 减少跨语言调用频次
- **批量操作**: 使用`BatchUpdateEntities`、`BatchSpawnEntities`等批量接口
- **事件队列**: 通过事件系统批量处理游戏事件，避免频繁的单次调用
- **缓存机制**: 将频繁访问的Lua函数映射为C# delegate并缓存

### 2. GC优化
- **值类型优化**: 对`Vector3`、`Quaternion`等使用`[GCOptimize]`标签
- **自定义结构体**: `GamePosition`、`GameStats`等游戏数据结构都已优化
- **避免装箱**: 使用预生成的适配代码而非反射

### 3. 内存管理
- **对象池**: EntityManager使用数组而非Dictionary，预分配内存
- **批量缓存**: 使用预分配的数组进行批量数据传输
- **定期GC**: GameManager定期调用`luaEnv.Tick()`清理Lua内存

## 🔧 配置说明

### XLua配置文件
```csharp
// Assets/Script/GameConfig.cs
[LuaCallCSharp]  // 生成C#到Lua的适配代码
[CSharpCallLua]  // 生成Lua到C#的适配代码  
[GCOptimize]     // 值类型GC优化
[BlackList]      // 排除不需要的方法
```

### 代码生成
1. 在Unity菜单中选择 `XLua -> Generate Code`
2. 生成的代码位于 `Assets/XLua/Gen/` 目录
3. 发布前必须生成代码以获得最佳性能

## 📊 性能监控

### 内置监控
- EntityManager提供实体数量统计
- GameManager提供Lua内存监控
- 按F1键显示调试信息

### 性能指标
- **目标**: 支持1000+实体同时运行
- **帧率**: 保持60FPS
- **内存**: Lua内存控制在合理范围

## 🎮 游戏系统架构

### 核心组件
1. **GameManager**: Lua环境管理和事件系统
2. **EntityManager**: 高性能实体管理
3. **GameConfig**: XLua配置和类型定义

### 数据流
```
Lua脚本 -> 批量操作接口 -> C#系统 -> Unity引擎
       <- 事件回调     <-        <-
```

## 💡 最佳实践

### Lua端优化
```lua
-- ✅ 好的做法：批量更新
function BatchUpdateEntities()
    local batchData = {}
    for id, entity in pairs(entities) do
        batchData[id] = {
            position = entity.position,
            stats = entity.stats
        }
    end
    GameManager.BatchUpdateEntities(batchData)
end

-- ❌ 避免：频繁单次调用
function BadUpdate()
    for id, entity in pairs(entities) do
        GameManager.UpdateEntity(id, entity.position, entity.stats)
    end
end
```

### C#端优化
```csharp
// ✅ 好的做法：缓存delegate
private Action<float> luaUpdate;
void Start() {
    luaUpdate = luaEnv.Global.Get<Action<float>>("Update");
}
void Update() {
    luaUpdate?.Invoke(Time.deltaTime);
}

// ❌ 避免：每次获取
void BadUpdate() {
    var luaUpdate = luaEnv.Global.Get<Action<float>>("Update");
    luaUpdate?.Invoke(Time.deltaTime);
}
```

### 值类型优化
```csharp
// ✅ 已优化的结构体
[GCOptimize]
public struct GamePosition {
    public float x, y, z, rotation;
}

// ✅ 使用优化的类型
public void UpdatePosition(GamePosition pos) {
    // 无GC分配
}
```

## 🚀 部署建议

### 开发阶段
- 启用热重载: `enableLuaHotReload = true`
- 启用性能监控: `enableProfiling = true`
- 使用反射模式进行快速迭代

### 发布阶段
- 生成所有适配代码
- 禁用热重载和调试功能
- 优化Lua脚本，移除调试代码

## 🔍 常见问题

### Q: 如何减少Lua-C#调用开销？
A: 使用批量操作接口，避免在Update中频繁调用单个实体操作

### Q: 如何处理大量实体？
A: EntityManager使用数组存储，支持10000个实体，按类型分类管理

### Q: 如何优化内存使用？
A: 使用GCOptimize标签的值类型，定期调用luaEnv.Tick()

### Q: 热更新如何实现？
A: 通过GameManager.ReloadLuaScript()重新加载Lua脚本

## 📈 性能测试

### 测试场景
- 1000个敌人同时移动和AI计算
- 100个技能特效同时播放
- 大量物品掉落和拾取

### 预期结果
- 帧率: 60FPS稳定
- 内存: Lua堆内存 < 50MB
- 延迟: 输入响应 < 16ms

## 🛠️ 调试工具

### Unity编辑器
- XLua菜单提供代码生成和清理功能
- Console显示详细的性能日志

### 运行时调试
- F1: 显示实体统计
- F2: 重载Lua脚本(开发模式)
- F3: 显示内存使用情况

## 📚 参考资料

- [XLua官方文档](https://github.com/Tencent/xLua)
- [Unity性能优化指南](https://docs.unity3d.com/Manual/BestPracticeGuides.html)
- [Lua性能优化技巧](http://lua-users.org/wiki/OptimisationTips)

---

**注意**: 这个架构专为暗黑2风格的ARPG游戏设计，重点优化了大量实体管理和实时战斗的性能需求。根据具体游戏需求，可能需要进一步调整和优化。
