using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;

/// <summary>
/// 游戏专用的XLua配置 - 针对暗黑2风格游戏优化
/// </summary>
public static class GameConfig
{
    // ==================== 核心游戏类型配置 ====================
    
    /// <summary>
    /// Lua调用C#的类型列表 - 游戏核心系统
    /// </summary>
    [LuaCallCSharp]
    public static List<Type> GameLuaCallCSharp = new List<Type>()
    {
        // Unity基础类型
        typeof(GameObject),
        typeof(Transform),
        typeof(Component),
        typeof(MonoBehaviour),
        typeof(Time),
        typeof(Input),
        typeof(Screen),
        typeof(Application),
        
        // 数学和物理类型 (已优化GC)
        typeof(Vector2),
        typeof(Vector3),
        typeof(Vector4),
        typeof(Quaternion),
        typeof(Color),
        typeof(Rect),
        typeof(Bounds),
        typeof(Ray),
        typeof(RaycastHit),
        typeof(Mathf),
        
        // 渲染相关
        typeof(Camera),
        typeof(Renderer),
        typeof(MeshRenderer),
        typeof(SpriteRenderer),
        typeof(Material),
        typeof(Texture),
        typeof(Texture2D),
        typeof(Sprite),
        
        // 动画系统
        typeof(Animator),
        typeof(Animation),
        typeof(AnimationClip),
        typeof(AnimationCurve),
        
        // 物理系统
        typeof(Rigidbody),
        typeof(Rigidbody2D),
        typeof(Collider),
        typeof(Collider2D),
        typeof(Physics),
        typeof(Physics2D),
        
        // 音频系统
        typeof(AudioSource),
        typeof(AudioClip),
        typeof(AudioListener),
        
        // UI系统
        typeof(Canvas),
        typeof(CanvasGroup),
        typeof(RectTransform),
        
        // 资源管理
        typeof(Resources),
        typeof(AssetBundle),
        
        // 集合类型
        typeof(List<int>),
        typeof(List<float>),
        typeof(List<string>),
        typeof(List<Vector3>),
        typeof(Dictionary<string, object>),
        
        // 游戏核心接口 (将在后续定义)
        typeof(IGameEntity),
        typeof(IGameSystem),
        typeof(IInputHandler),
        typeof(ICombatSystem),
        typeof(IInventorySystem),
    };

    /// <summary>
    /// C#调用Lua的委托类型
    /// </summary>
    [CSharpCallLua]
    public static List<Type> GameCSharpCallLua = new List<Type>()
    {
        // 基础委托
        typeof(Action),
        typeof(Action<float>),
        typeof(Action<int>),
        typeof(Action<string>),
        typeof(Action<bool>),
        typeof(Action<Vector3>),
        typeof(Action<GameObject>),
        
        // 函数委托
        typeof(Func<float>),
        typeof(Func<int>),
        typeof(Func<string>),
        typeof(Func<bool>),
        typeof(Func<Vector3>),
        typeof(Func<GameObject>),
        
        // 游戏特定委托
        typeof(GameEventHandler),
        typeof(EntityUpdateHandler),
        typeof(CombatEventHandler),
        typeof(ItemEventHandler),
        typeof(SkillEventHandler),
    };

    /// <summary>
    /// GC优化的值类型列表
    /// </summary>
    [GCOptimize]
    public static List<Type> GameGCOptimize = new List<Type>()
    {
        typeof(Vector2),
        typeof(Vector3),
        typeof(Vector4),
        typeof(Quaternion),
        typeof(Color),
        typeof(Rect),
        typeof(Bounds),
        typeof(Ray),
        
        // 游戏自定义值类型
        typeof(GamePosition),
        typeof(GameStats),
        typeof(ItemData),
        typeof(SkillData),
    };

    /// <summary>
    /// 黑名单 - 不生成代码的方法
    /// </summary>
    [BlackList]
    public static List<List<string>> GameBlackList = new List<List<string>>()
    {
        new List<string>(){"UnityEngine.WWW", "movie"},
        new List<string>(){"UnityEngine.Texture2D", "alphaIsTransparency"},
        new List<string>(){"UnityEngine.Security", "GetChainOfTrustValue"},
        new List<string>(){"UnityEngine.CanvasRenderer", "onRequestRebuild"},
        new List<string>(){"UnityEngine.Light", "areaSize"},
        new List<string>(){"UnityEngine.AnimatorOverrideController", "PerformOverrideClipListCleanup"},
        #if UNITY_WEBGL
        new List<string>(){"UnityEngine.AudioSource", "SetCustomCurve"},
        new List<string>(){"UnityEngine.AudioSource", "GetCustomCurve"},
        #endif
    };
}

// ==================== 游戏委托定义 ====================

/// <summary>
/// 游戏事件处理委托
/// </summary>
public delegate void GameEventHandler(string eventName, params object[] args);

/// <summary>
/// 实体更新处理委托
/// </summary>
public delegate void EntityUpdateHandler(float deltaTime);

/// <summary>
/// 战斗事件处理委托
/// </summary>
public delegate void CombatEventHandler(int attackerId, int targetId, float damage);

/// <summary>
/// 物品事件处理委托
/// </summary>
public delegate void ItemEventHandler(int itemId, int count);

/// <summary>
/// 技能事件处理委托
/// </summary>
public delegate void SkillEventHandler(int skillId, Vector3 position, int targetId);

// ==================== 游戏核心接口 ====================

/// <summary>
/// 游戏实体接口
/// </summary>
public interface IGameEntity
{
    int EntityId { get; }
    Vector3 Position { get; set; }
    bool IsActive { get; set; }
    void Update(float deltaTime);
    void Destroy();
}

/// <summary>
/// 游戏系统接口
/// </summary>
public interface IGameSystem
{
    string SystemName { get; }
    bool IsEnabled { get; set; }
    void Initialize();
    void Update(float deltaTime);
    void Shutdown();
}

/// <summary>
/// 输入处理接口
/// </summary>
public interface IInputHandler
{
    void HandleInput();
    void RegisterKeyBinding(KeyCode key, Action callback);
    void UnregisterKeyBinding(KeyCode key);
}

/// <summary>
/// 战斗系统接口
/// </summary>
public interface ICombatSystem : IGameSystem
{
    void Attack(int attackerId, int targetId);
    void ApplyDamage(int targetId, float damage);
    void ApplyHealing(int targetId, float healing);
    bool IsInCombat(int entityId);
}

/// <summary>
/// 背包系统接口
/// </summary>
public interface IInventorySystem : IGameSystem
{
    bool AddItem(int itemId, int count);
    bool RemoveItem(int itemId, int count);
    int GetItemCount(int itemId);
    bool HasItem(int itemId);
}

// ==================== 游戏值类型定义 ====================

/// <summary>
/// 游戏位置信息 (GC优化)
/// </summary>
[GCOptimize]
public struct GamePosition
{
    public float x;
    public float y;
    public float z;
    public float rotation;
    
    public GamePosition(float x, float y, float z, float rotation = 0f)
    {
        this.x = x;
        this.y = y;
        this.z = z;
        this.rotation = rotation;
    }
    
    public Vector3 ToVector3()
    {
        return new Vector3(x, y, z);
    }
    
    public static implicit operator Vector3(GamePosition pos)
    {
        return new Vector3(pos.x, pos.y, pos.z);
    }
}

/// <summary>
/// 游戏属性数据 (GC优化)
/// </summary>
[GCOptimize]
public struct GameStats
{
    public int health;
    public int maxHealth;
    public int mana;
    public int maxMana;
    public int attack;
    public int defense;
    public float speed;
    public int level;
    public int experience;
}

/// <summary>
/// 物品数据 (GC优化)
/// </summary>
[GCOptimize]
public struct ItemData
{
    public int itemId;
    public int count;
    public int quality;
    public int durability;
    public int maxDurability;
}

/// <summary>
/// 技能数据 (GC优化)
/// </summary>
[GCOptimize]
public struct SkillData
{
    public int skillId;
    public int level;
    public float cooldown;
    public float remainingCooldown;
    public int manaCost;
    public float damage;
    public float range;
}
