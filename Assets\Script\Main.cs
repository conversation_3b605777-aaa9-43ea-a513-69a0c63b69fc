using UnityEngine;

/// <summary>
/// 主入口脚本 - 暗黑2风格游戏
/// 负责初始化和协调各个游戏系统
/// </summary>
public class Main : MonoBehaviour
{
    [Header("游戏系统")]
    public GameManager gameManager;
    public EntityManager entityManager;

    [Header("游戏配置")]
    public bool autoInitialize = true;
    public bool enableDebugUI = true;

    private bool isInitialized = false;

    void Awake()
    {
        // 确保GameManager存在
        if (gameManager == null)
        {
            gameManager = GameManager.Instance;
        }

        // 创建EntityManager
        if (entityManager == null)
        {
            GameObject entityManagerGO = new GameObject("EntityManager");
            entityManager = entityManagerGO.AddComponent<EntityManager>();
            entityManagerGO.transform.SetParent(transform);
        }
    }

    void Start()
    {
        if (autoInitialize)
        {
            InitializeGame();
        }
    }

    void Update()
    {
        if (!isInitialized) return;

        // 更新实体管理器
        entityManager?.Update(Time.deltaTime);

        // 调试信息
        if (enableDebugUI && Input.GetKeyDown(KeyCode.F1))
        {
            ShowDebugInfo();
        }
    }

    /// <summary>
    /// 初始化游戏
    /// </summary>
    public void InitializeGame()
    {
        Debug.Log("Main: 开始初始化游戏系统");

        try
        {
            // 初始化实体管理器
            entityManager.Initialize();

            // 等待GameManager初始化完成
            if (gameManager != null)
            {
                Debug.Log("Main: 游戏系统初始化完成");
                isInitialized = true;
            }
            else
            {
                Debug.LogError("Main: GameManager未找到，初始化失败");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Main: 游戏初始化失败 - {e.Message}");
        }
    }

    /// <summary>
    /// 显示调试信息
    /// </summary>
    private void ShowDebugInfo()
    {
        if (entityManager != null)
        {
            Debug.Log($"实体统计 - 活跃: {entityManager.GetActiveEntityCount()}, 空闲: {entityManager.GetFreeEntityCount()}");
        }
    }

    void OnDestroy()
    {
        // 清理系统
        entityManager?.Shutdown();
        Debug.Log("Main: 游戏系统已清理");
    }
}
