Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.15f1 (faa32412f6c9) revision 16425764'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22621) 64bit ProfessionalWorkstation' Language: 'zh' Physical Memory: 65137 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-13T09:50:25Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/Unity/Unity6/My project
-logFile
Logs/AssetImportWorker0.log
-srvPort
52194
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/Unity/Unity6/My project
F:/Unity/Unity6/My project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [45064]  Target information:

Player connection [45064]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 267770179 [EditorId] 267770179 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-2DTR2RN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [45064]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 267770179 [EditorId] 267770179 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-2DTR2RN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [45064]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 267770179 [EditorId] 267770179 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-2DTR2RN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [45064] Host joined multi-casting on [***********:54997]...
Player connection [45064] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 9.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.15f1 (faa32412f6c9)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Unity/Unity6/My project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 3070 (ID=0x2484)
    Vendor:          NVIDIA
    VRAM:            8018 MB
    App VRAM Budget: 7250 MB
    Driver:          32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56216
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004039 seconds.
- Loaded All Assemblies, in  0.756 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.508 seconds
Domain Reload Profiling: 1262ms
	BeginReloadAssembly (244ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (89ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (298ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (294ms)
			TypeCache.Refresh (292ms)
				TypeCache.ScanAssembly (266ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (510ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (430ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (73ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (187ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.252 seconds
Refreshing native plugins compatible for Editor in 5.59 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.279 seconds
Domain Reload Profiling: 2521ms
	BeginReloadAssembly (284ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (817ms)
		LoadAssemblies (637ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (349ms)
			TypeCache.Refresh (266ms)
				TypeCache.ScanAssembly (233ms)
			BuildScriptInfoCaches (64ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1280ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (949ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (249ms)
			ProcessInitializeOnLoadAttributes (581ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6207 unused Assets / (6.3 MB). Loaded Objects now: 6832.
Memory consumption went from 142.6 MB to 136.3 MB.
Total: 20.978300 ms (FindLiveObjects: 1.445100 ms CreateObjectMapping: 3.017500 ms MarkObjects: 12.516100 ms  DeleteObjects: 3.996800 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.993 seconds
Refreshing native plugins compatible for Editor in 8.87 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.185 seconds
Domain Reload Profiling: 2179ms
	BeginReloadAssembly (284ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (593ms)
		LoadAssemblies (510ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1186ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (942ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (245ms)
			ProcessInitializeOnLoadAttributes (573ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 4.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6204 unused Assets / (6.7 MB). Loaded Objects now: 6848.
Memory consumption went from 147.4 MB to 140.7 MB.
Total: 13.749300 ms (FindLiveObjects: 0.761300 ms CreateObjectMapping: 0.808100 ms MarkObjects: 5.106200 ms  DeleteObjects: 7.072100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6848.
Memory consumption went from 144.5 MB to 141.5 MB.
Total: 7.128900 ms (FindLiveObjects: 0.406600 ms CreateObjectMapping: 0.279800 ms MarkObjects: 4.796100 ms  DeleteObjects: 1.644900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 198584.092717 seconds.
  path: Assets/Scenes
  artifactKey: Guid(9c53962885c2c4f449125a979d6ad240) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes using Guid(9c53962885c2c4f449125a979d6ad240) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd65b75f456837a3494a5e6e744543932') in 0.0123667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.977556 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '092f7974db3fb175701bdf9959c1e23b') in 0.0005765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 7.562725 seconds.
  path: Assets/Settings
  artifactKey: Guid(75b2fd9cd04c94e7693616260cd451f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings using Guid(75b2fd9cd04c94e7693616260cd451f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a5221c5f654ded9c4debe0c00ce9f6f0') in 0.0007205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.015664 seconds.
  path: Assets/Settings/Mobile_Renderer.asset
  artifactKey: Guid(65bc7dbf4170f435aa868c779acfb082) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Mobile_Renderer.asset using Guid(65bc7dbf4170f435aa868c779acfb082) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a200c5b489e2ba3068edb0e8d1ae3e52') in 0.0323253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Settings/PC_RPAsset.asset
  artifactKey: Guid(4b83569d67af61e458304325a23e5dfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/PC_RPAsset.asset using Guid(4b83569d67af61e458304325a23e5dfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f920ee52e08e9c71496953f432d613b4') in 0.0071146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Settings/PC_Renderer.asset
  artifactKey: Guid(f288ae1f4751b564a96ac7587541f7a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/PC_Renderer.asset using Guid(f288ae1f4751b564a96ac7587541f7a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1af73301777be984e2af80335327f964') in 0.0024314 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.202 seconds
Refreshing native plugins compatible for Editor in 5.59 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.184 seconds
Domain Reload Profiling: 2385ms
	BeginReloadAssembly (308ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (755ms)
		LoadAssemblies (614ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (290ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (249ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1184ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (951ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (266ms)
			ProcessInitializeOnLoadAttributes (571ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 6.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6204 unused Assets / (5.8 MB). Loaded Objects now: 6853.
Memory consumption went from 148.0 MB to 142.3 MB.
Total: 15.294400 ms (FindLiveObjects: 1.037900 ms CreateObjectMapping: 1.045600 ms MarkObjects: 5.807500 ms  DeleteObjects: 7.401300 ms)

Prepare: number of updated asset objects reloaded= 5
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.080 seconds
Refreshing native plugins compatible for Editor in 5.84 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.042 seconds
Domain Reload Profiling: 2119ms
	BeginReloadAssembly (301ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (646ms)
		LoadAssemblies (552ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (246ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (212ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1043ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (820ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (264ms)
			ProcessInitializeOnLoadAttributes (463ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (16ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 3.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6204 unused Assets / (6.5 MB). Loaded Objects now: 6870.
Memory consumption went from 149.4 MB to 142.9 MB.
Total: 11.353400 ms (FindLiveObjects: 0.830300 ms CreateObjectMapping: 0.629200 ms MarkObjects: 5.227300 ms  DeleteObjects: 4.665200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.066 seconds
Refreshing native plugins compatible for Editor in 4.45 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.061 seconds
Domain Reload Profiling: 2131ms
	BeginReloadAssembly (294ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (656ms)
		LoadAssemblies (556ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (248ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (219ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1062ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (828ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (264ms)
			ProcessInitializeOnLoadAttributes (470ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 4.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6204 unused Assets / (6.3 MB). Loaded Objects now: 6872.
Memory consumption went from 150.9 MB to 144.6 MB.
Total: 11.303100 ms (FindLiveObjects: 0.803800 ms CreateObjectMapping: 0.670800 ms MarkObjects: 6.021100 ms  DeleteObjects: 3.805500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6186 unused Assets / (6.7 MB). Loaded Objects now: 6873.
Memory consumption went from 152.5 MB to 145.7 MB.
Total: 20.291100 ms (FindLiveObjects: 1.125500 ms CreateObjectMapping: 1.691800 ms MarkObjects: 8.572100 ms  DeleteObjects: 8.899400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.026 seconds
Refreshing native plugins compatible for Editor in 4.04 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.064 seconds
Domain Reload Profiling: 2089ms
	BeginReloadAssembly (303ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (622ms)
		LoadAssemblies (544ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (195ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1064ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (854ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (265ms)
			ProcessInitializeOnLoadAttributes (487ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (17ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 3.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6205 unused Assets / (6.4 MB). Loaded Objects now: 6875.
Memory consumption went from 153.8 MB to 147.5 MB.
Total: 13.584000 ms (FindLiveObjects: 1.903200 ms CreateObjectMapping: 1.726100 ms MarkObjects: 5.080000 ms  DeleteObjects: 4.872200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.797 seconds
Refreshing native plugins compatible for Editor in 9.94 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.157 seconds
Domain Reload Profiling: 4955ms
	BeginReloadAssembly (601ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (201ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (2934ms)
		LoadAssemblies (3084ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (278ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (236ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1157ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (884ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (269ms)
			ProcessInitializeOnLoadAttributes (523ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 7.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.4 MB). Loaded Objects now: 6932.
Memory consumption went from 156.3 MB to 149.9 MB.
Total: 15.987600 ms (FindLiveObjects: 0.810600 ms CreateObjectMapping: 0.782100 ms MarkObjects: 4.679000 ms  DeleteObjects: 9.713200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.041 seconds
Refreshing native plugins compatible for Editor in 12.17 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.055 seconds
Domain Reload Profiling: 2096ms
	BeginReloadAssembly (296ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (635ms)
		LoadAssemblies (529ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (217ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1055ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (790ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (228ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 8.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.8 MB). Loaded Objects now: 6934.
Memory consumption went from 158.5 MB to 151.7 MB.
Total: 12.948500 ms (FindLiveObjects: 0.774800 ms CreateObjectMapping: 0.723900 ms MarkObjects: 6.656900 ms  DeleteObjects: 4.791500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6934.
Memory consumption went from 155.4 MB to 152.4 MB.
Total: 11.056300 ms (FindLiveObjects: 0.464800 ms CreateObjectMapping: 0.364100 ms MarkObjects: 8.962400 ms  DeleteObjects: 1.263300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.033 seconds
Refreshing native plugins compatible for Editor in 13.00 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.208 seconds
Domain Reload Profiling: 2241ms
	BeginReloadAssembly (318ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (622ms)
		LoadAssemblies (537ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (240ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (207ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1209ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (982ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (291ms)
			ProcessInitializeOnLoadAttributes (573ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 6.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.4 MB). Loaded Objects now: 6936.
Memory consumption went from 161.4 MB to 155.0 MB.
Total: 15.336000 ms (FindLiveObjects: 2.976800 ms CreateObjectMapping: 1.843700 ms MarkObjects: 6.236300 ms  DeleteObjects: 4.278000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 6.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6936.
Memory consumption went from 158.3 MB to 155.3 MB.
Total: 7.069200 ms (FindLiveObjects: 0.449700 ms CreateObjectMapping: 0.309600 ms MarkObjects: 5.285300 ms  DeleteObjects: 1.023200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.303 seconds
Refreshing native plugins compatible for Editor in 11.78 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.261 seconds
Domain Reload Profiling: 2565ms
	BeginReloadAssembly (362ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (800ms)
		LoadAssemblies (682ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (266ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1262ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1006ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (303ms)
			ProcessInitializeOnLoadAttributes (584ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 9.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.7 MB). Loaded Objects now: 6938.
Memory consumption went from 164.3 MB to 157.7 MB.
Total: 17.251800 ms (FindLiveObjects: 1.055700 ms CreateObjectMapping: 1.097800 ms MarkObjects: 7.817200 ms  DeleteObjects: 7.275700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 13.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6938.
Memory consumption went from 161.2 MB to 158.2 MB.
Total: 8.182900 ms (FindLiveObjects: 0.616500 ms CreateObjectMapping: 0.431800 ms MarkObjects: 5.255300 ms  DeleteObjects: 1.877100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.170 seconds
Refreshing native plugins compatible for Editor in 7.55 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.288 seconds
Domain Reload Profiling: 2460ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (705ms)
		LoadAssemblies (626ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (249ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (215ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1289ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1047ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (357ms)
			ProcessInitializeOnLoadAttributes (579ms)
			ProcessInitializeOnLoadMethodAttributes (77ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 8.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.6 MB). Loaded Objects now: 6940.
Memory consumption went from 167.3 MB to 160.7 MB.
Total: 16.100200 ms (FindLiveObjects: 1.093300 ms CreateObjectMapping: 0.711200 ms MarkObjects: 6.369500 ms  DeleteObjects: 7.924500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 8.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6940.
Memory consumption went from 164.2 MB to 161.1 MB.
Total: 12.187800 ms (FindLiveObjects: 0.440800 ms CreateObjectMapping: 0.370900 ms MarkObjects: 7.711600 ms  DeleteObjects: 3.662000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.018 seconds
Refreshing native plugins compatible for Editor in 12.04 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.179 seconds
Domain Reload Profiling: 2198ms
	BeginReloadAssembly (291ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (622ms)
		LoadAssemblies (503ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (250ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (219ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1179ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (957ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (267ms)
			ProcessInitializeOnLoadAttributes (562ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 6.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.3 MB). Loaded Objects now: 6942.
Memory consumption went from 170.2 MB to 163.9 MB.
Total: 15.370200 ms (FindLiveObjects: 1.226400 ms CreateObjectMapping: 1.922600 ms MarkObjects: 7.939200 ms  DeleteObjects: 4.280600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6942.
Memory consumption went from 167.1 MB to 164.1 MB.
Total: 10.323000 ms (FindLiveObjects: 1.067500 ms CreateObjectMapping: 0.978500 ms MarkObjects: 7.079200 ms  DeleteObjects: 1.195000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1656.635239 seconds.
  path: Assets/XLua/Examples/09_GenericMethod/GenericMethod/ReflectionProbe-0.exr
  artifactKey: Guid(75e7ac6591da2e547803e332f9ef5020) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/09_GenericMethod/GenericMethod/ReflectionProbe-0.exr using Guid(75e7ac6591da2e547803e332f9ef5020) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '89471bd76c8b6bbc03410b619b135157') in 0.6903949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.867 seconds
Refreshing native plugins compatible for Editor in 10.27 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.004 seconds
Domain Reload Profiling: 3872ms
	BeginReloadAssembly (498ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (129ms)
	RebuildCommonClasses (91ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (87ms)
	LoadAllAssembliesAndSetupDomain (1163ms)
		LoadAssemblies (937ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (443ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (378ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (2006ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (453ms)
			ProcessInitializeOnLoadAttributes (725ms)
			ProcessInitializeOnLoadMethodAttributes (260ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (133ms)
Refreshing native plugins compatible for Editor in 17.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.3 MB). Loaded Objects now: 6999.
Memory consumption went from 160.5 MB to 154.3 MB.
Total: 22.810900 ms (FindLiveObjects: 1.326900 ms CreateObjectMapping: 2.231000 ms MarkObjects: 10.840300 ms  DeleteObjects: 8.408500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.932 seconds
Script error (EntityManager): Update() can not take parameters.
Refreshing native plugins compatible for Editor in 9.82 ms, found 4 plugins.
Script error (EntityManager): Update() can not take parameters.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.220 seconds
Domain Reload Profiling: 6153ms
	BeginReloadAssembly (818ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (183ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (3865ms)
		LoadAssemblies (4041ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (382ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (336ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1221ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (980ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (286ms)
			ProcessInitializeOnLoadAttributes (588ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (17ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 11.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6279 unused Assets / (6.6 MB). Loaded Objects now: 7004.
Memory consumption went from 161.9 MB to 155.2 MB.
Total: 20.951400 ms (FindLiveObjects: 1.177100 ms CreateObjectMapping: 1.230700 ms MarkObjects: 10.846000 ms  DeleteObjects: 7.694200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4148.978709 seconds.
  path: Assets/Script/Main.cs
  artifactKey: Guid(57229493ac4a0444fa99d5ab9c05aab9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Main.cs using Guid(57229493ac4a0444fa99d5ab9c05aab9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '396b9cb2bf5b050ed2ed7651c4576291') in 0.0044485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Script error (EntityManager): Update() can not take parameters.
Refreshing native plugins compatible for Editor in 17.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6244 unused Assets / (6.3 MB). Loaded Objects now: 7004.
Memory consumption went from 160.4 MB to 154.1 MB.
Total: 30.914600 ms (FindLiveObjects: 1.197800 ms CreateObjectMapping: 1.594400 ms MarkObjects: 19.706200 ms  DeleteObjects: 8.414000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.462 seconds
Script error (EntityManager): Update() can not take parameters.
Refreshing native plugins compatible for Editor in 9.03 ms, found 4 plugins.
Script error (EntityManager): Update() can not take parameters.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.215 seconds
Domain Reload Profiling: 2681ms
	BeginReloadAssembly (589ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (172ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (748ms)
		LoadAssemblies (729ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (253ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (219ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1215ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (955ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (262ms)
			ProcessInitializeOnLoadAttributes (579ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 8.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6279 unused Assets / (6.0 MB). Loaded Objects now: 7006.
Memory consumption went from 161.9 MB to 155.9 MB.
Total: 13.751900 ms (FindLiveObjects: 1.302900 ms CreateObjectMapping: 0.885200 ms MarkObjects: 5.345400 ms  DeleteObjects: 6.216700 ms)

Prepare: number of updated asset objects reloaded= 0
