XLua内置两个小工具进行性能方面问题的分析：一个是Lua函数，Lua调用C#函数的时长（不一定等同于CPU耗时，比如协程yield出去那段时间也会被算入调用时间）分析工具；一个是内存泄漏定位工具。

## 函数调用时长分析工具

### 典型使用案例：

说明：

api很简单，就三个，start和stop都是无参数，也很好理解，分别是统计开始以及结束。在start以及stop之间可以多次调用report（也可以考虑不调stop）。每次report会得到从start到调用report为止的函数时长统计报告（以字符串返回）。report函数只有一个可选参数，可以指明按照总时间（参数是字符串的”TOTAL”，这个是默认值），平均每次调用时间（“AVERAGE”），以及调用次数（“CALLED”）来排序。
典型的一个时长统计报告如下：

第一列是函数名

第二列是源代码，如果是lua文件将会统计到文件，行号，如果是C#的导出代码，将会标注[C#]，如果是C函数，标准为[C]。

后面几列分别是总时间，平均每次调用时间，占总统计时间的百分比，以及调用次数。

## 内存泄漏定位工具

### 典型使用案例：

说明：

api就两个，total获取lua虚拟机的内存占用，单位是Kbytes，以lua number返回。而snapshot返回当前内存快照信息，一个典型的快照报告如下：

第一列是table变量名；

第二列是table的大小，如果该table下有子table也会被统计到；

第三列是变量的类型，UPVALUE代表是闭包里头的变量（内层函数对外层local变量的引用），GLOBAL是全局变量，REGISTRY是C侧（包含虚拟机内部的）的一些私有数据。一般主要关注前面两种；

第四列是变量的ID，其实就是内存指针，如果两次报告间没被重新分配，该ID是不变的，便于识别是否同一table；

最后一列是一些附加信息，比如闭包变量可能存在很多同名的，这里会通过有那些函数引用了该变量来协助定位。

如何定位内存泄漏：通过memory.total检测出内存的持续增长，然后通过memory.snapshot定位出哪里泄漏（lua测的内存泄漏都是表现为往某table添加了数据忘了删除）。