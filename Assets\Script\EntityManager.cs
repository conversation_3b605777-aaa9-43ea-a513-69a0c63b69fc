using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;

/// <summary>
/// 实体管理器 - 高性能实体系统
/// 专为暗黑2风格游戏优化，支持大量实体的高效管理
/// </summary>
public class EntityManager : MonoBehaviour, IGameSystem
{
    #region IGameSystem实现
    public string SystemName => "EntityManager";
    public bool IsEnabled { get; set; } = true;
    #endregion

    #region 实体存储
    // 使用数组而非Dictionary提高性能
    private const int MAX_ENTITIES = 10000;
    private GameEntity[] entities = new GameEntity[MAX_ENTITIES];
    private bool[] entityActive = new bool[MAX_ENTITIES];
    private Queue<int> freeEntityIds = new Queue<int>();
    private int nextEntityId = 0;
    
    // 分类存储，便于批量处理
    private List<int> playerEntities = new List<int>();
    private List<int> enemyEntities = new List<int>();
    private List<int> itemEntities = new List<int>();
    private List<int> projectileEntities = new List<int>();
    #endregion

    #region 批量更新数据
    // 预分配数组，避免GC
    private Vector3[] positionBuffer = new Vector3[MAX_ENTITIES];
    private GameStats[] statsBuffer = new GameStats[MAX_ENTITIES];
    private int[] updateEntityIds = new int[MAX_ENTITIES];
    private int updateCount = 0;
    
    // Lua回调缓存
    private Action<int[]> luaOnEntitiesSpawned;
    private Action<int[]> luaOnEntitiesDestroyed;
    private Action<int[], Vector3[], GameStats[]> luaOnEntitiesUpdated;
    #endregion

    #region 性能统计
    [Header("性能监控")]
    public bool enableProfiling = false;
    private int frameEntityCount = 0;
    private float lastProfileTime = 0f;
    private const float PROFILE_INTERVAL = 1.0f;
    #endregion

    #region 初始化
    public void Initialize()
    {
        // 初始化空闲ID队列
        for (int i = 0; i < MAX_ENTITIES; i++)
        {
            freeEntityIds.Enqueue(i);
            entityActive[i] = false;
        }
        
        // 缓存Lua回调
        CacheLuaCallbacks();
        
        Debug.Log("EntityManager: 初始化完成");
    }

    private void CacheLuaCallbacks()
    {
        var luaEnv = GameManager.Instance?.GetComponent<GameManager>();
        if (luaEnv != null)
        {
            try
            {
                // 这里需要从GameManager获取LuaEnv，暂时注释
                // luaOnEntitiesSpawned = luaEnv.Global.Get<Action<int[]>>("OnEntitiesSpawned");
                // luaOnEntitiesDestroyed = luaEnv.Global.Get<Action<int[]>>("OnEntitiesDestroyed");
                // luaOnEntitiesUpdated = luaEnv.Global.Get<Action<int[], Vector3[], GameStats[]>>("OnEntitiesUpdated");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"EntityManager: Lua回调缓存失败 - {e.Message}");
            }
        }
    }

    public void Shutdown()
    {
        // 清理所有实体
        for (int i = 0; i < MAX_ENTITIES; i++)
        {
            if (entityActive[i])
            {
                DestroyEntityInternal(i);
            }
        }
        
        Debug.Log("EntityManager: 系统关闭");
    }
    #endregion

    #region 实体创建和销毁
    /// <summary>
    /// 创建实体
    /// </summary>
    public int CreateEntity(EntityType type, Vector3 position, GameStats stats)
    {
        if (freeEntityIds.Count == 0)
        {
            Debug.LogError("EntityManager: 实体数量已达上限");
            return -1;
        }

        int entityId = freeEntityIds.Dequeue();
        
        entities[entityId] = new GameEntity
        {
            id = entityId,
            type = type,
            position = position,
            stats = stats,
            isActive = true,
            createTime = Time.time
        };
        
        entityActive[entityId] = true;
        
        // 添加到分类列表
        AddToTypeList(entityId, type);
        
        return entityId;
    }

    /// <summary>
    /// 批量创建实体
    /// </summary>
    public int[] CreateEntities(EntityType[] types, Vector3[] positions, GameStats[] stats)
    {
        int count = Mathf.Min(types.Length, positions.Length, stats.Length);
        int[] entityIds = new int[count];
        
        for (int i = 0; i < count; i++)
        {
            entityIds[i] = CreateEntity(types[i], positions[i], stats[i]);
        }
        
        // 通知Lua
        luaOnEntitiesSpawned?.Invoke(entityIds);
        
        return entityIds;
    }

    /// <summary>
    /// 销毁实体
    /// </summary>
    public void DestroyEntity(int entityId)
    {
        if (!IsValidEntity(entityId)) return;
        
        DestroyEntityInternal(entityId);
        
        // 通知Lua
        luaOnEntitiesDestroyed?.Invoke(new int[] { entityId });
    }

    /// <summary>
    /// 批量销毁实体
    /// </summary>
    public void DestroyEntities(int[] entityIds)
    {
        List<int> destroyedIds = new List<int>();
        
        foreach (int entityId in entityIds)
        {
            if (IsValidEntity(entityId))
            {
                DestroyEntityInternal(entityId);
                destroyedIds.Add(entityId);
            }
        }
        
        // 通知Lua
        if (destroyedIds.Count > 0)
        {
            luaOnEntitiesDestroyed?.Invoke(destroyedIds.ToArray());
        }
    }

    private void DestroyEntityInternal(int entityId)
    {
        if (!IsValidEntity(entityId)) return;
        
        // 从分类列表移除
        RemoveFromTypeList(entityId, entities[entityId].type);
        
        // 标记为非活跃
        entityActive[entityId] = false;
        entities[entityId].isActive = false;
        
        // 回收ID
        freeEntityIds.Enqueue(entityId);
    }
    #endregion

    #region 实体查询和更新
    /// <summary>
    /// 获取实体
    /// </summary>
    public GameEntity GetEntity(int entityId)
    {
        if (IsValidEntity(entityId))
        {
            return entities[entityId];
        }
        return default(GameEntity);
    }

    /// <summary>
    /// 更新实体位置
    /// </summary>
    public void UpdateEntityPosition(int entityId, Vector3 position)
    {
        if (IsValidEntity(entityId))
        {
            entities[entityId].position = position;
        }
    }

    /// <summary>
    /// 批量更新实体位置
    /// </summary>
    public void UpdateEntityPositions(int[] entityIds, Vector3[] positions)
    {
        int count = Mathf.Min(entityIds.Length, positions.Length);
        
        for (int i = 0; i < count; i++)
        {
            if (IsValidEntity(entityIds[i]))
            {
                entities[entityIds[i]].position = positions[i];
            }
        }
    }

    /// <summary>
    /// 更新实体属性
    /// </summary>
    public void UpdateEntityStats(int entityId, GameStats stats)
    {
        if (IsValidEntity(entityId))
        {
            entities[entityId].stats = stats;
        }
    }

    /// <summary>
    /// 获取指定类型的所有实体
    /// </summary>
    public int[] GetEntitiesByType(EntityType type)
    {
        switch (type)
        {
            case EntityType.Player:
                return playerEntities.ToArray();
            case EntityType.Enemy:
                return enemyEntities.ToArray();
            case EntityType.Item:
                return itemEntities.ToArray();
            case EntityType.Projectile:
                return projectileEntities.ToArray();
            default:
                return new int[0];
        }
    }

    /// <summary>
    /// 获取范围内的实体
    /// </summary>
    public int[] GetEntitiesInRange(Vector3 center, float radius, EntityType type = EntityType.All)
    {
        List<int> result = new List<int>();
        float radiusSqr = radius * radius;
        
        List<int> searchList = GetTypeList(type);
        
        foreach (int entityId in searchList)
        {
            if (IsValidEntity(entityId))
            {
                float distanceSqr = (entities[entityId].position - center).sqrMagnitude;
                if (distanceSqr <= radiusSqr)
                {
                    result.Add(entityId);
                }
            }
        }
        
        return result.ToArray();
    }
    #endregion

    #region 系统更新
    public void Update(float deltaTime)
    {
        if (!IsEnabled) return;
        
        // 收集需要更新的实体数据
        CollectUpdateData();
        
        // 批量通知Lua
        if (updateCount > 0)
        {
            luaOnEntitiesUpdated?.Invoke(updateEntityIds, positionBuffer, statsBuffer);
        }
        
        // 性能统计
        if (enableProfiling)
        {
            UpdateProfiling();
        }
    }

    private void CollectUpdateData()
    {
        updateCount = 0;
        
        for (int i = 0; i < MAX_ENTITIES && updateCount < MAX_ENTITIES; i++)
        {
            if (entityActive[i])
            {
                updateEntityIds[updateCount] = i;
                positionBuffer[updateCount] = entities[i].position;
                statsBuffer[updateCount] = entities[i].stats;
                updateCount++;
            }
        }
    }

    private void UpdateProfiling()
    {
        frameEntityCount = GetActiveEntityCount();
        
        if (Time.time - lastProfileTime >= PROFILE_INTERVAL)
        {
            Debug.Log($"EntityManager: 活跃实体数量: {frameEntityCount}/{MAX_ENTITIES}");
            lastProfileTime = Time.time;
        }
    }
    #endregion

    #region 工具方法
    private bool IsValidEntity(int entityId)
    {
        return entityId >= 0 && entityId < MAX_ENTITIES && entityActive[entityId];
    }

    private void AddToTypeList(int entityId, EntityType type)
    {
        switch (type)
        {
            case EntityType.Player:
                playerEntities.Add(entityId);
                break;
            case EntityType.Enemy:
                enemyEntities.Add(entityId);
                break;
            case EntityType.Item:
                itemEntities.Add(entityId);
                break;
            case EntityType.Projectile:
                projectileEntities.Add(entityId);
                break;
        }
    }

    private void RemoveFromTypeList(int entityId, EntityType type)
    {
        switch (type)
        {
            case EntityType.Player:
                playerEntities.Remove(entityId);
                break;
            case EntityType.Enemy:
                enemyEntities.Remove(entityId);
                break;
            case EntityType.Item:
                itemEntities.Remove(entityId);
                break;
            case EntityType.Projectile:
                projectileEntities.Remove(entityId);
                break;
        }
    }

    private List<int> GetTypeList(EntityType type)
    {
        switch (type)
        {
            case EntityType.Player:
                return playerEntities;
            case EntityType.Enemy:
                return enemyEntities;
            case EntityType.Item:
                return itemEntities;
            case EntityType.Projectile:
                return projectileEntities;
            default:
                // 返回所有实体
                List<int> allEntities = new List<int>();
                for (int i = 0; i < MAX_ENTITIES; i++)
                {
                    if (entityActive[i])
                    {
                        allEntities.Add(i);
                    }
                }
                return allEntities;
        }
    }

    public int GetActiveEntityCount()
    {
        int count = 0;
        for (int i = 0; i < MAX_ENTITIES; i++)
        {
            if (entityActive[i]) count++;
        }
        return count;
    }

    public int GetFreeEntityCount()
    {
        return freeEntityIds.Count;
    }
    #endregion
}

/// <summary>
/// 实体类型枚举
/// </summary>
public enum EntityType
{
    All = 0,
    Player = 1,
    Enemy = 2,
    Item = 3,
    Projectile = 4,
    NPC = 5,
    Environment = 6
}

/// <summary>
/// 游戏实体结构 (值类型，性能优化)
/// </summary>
[System.Serializable]
public struct GameEntity
{
    public int id;
    public EntityType type;
    public Vector3 position;
    public GameStats stats;
    public bool isActive;
    public float createTime;
    
    // 扩展属性
    public int prefabId;
    public float rotation;
    public Vector3 velocity;
    public int ownerId; // 归属玩家ID
}
