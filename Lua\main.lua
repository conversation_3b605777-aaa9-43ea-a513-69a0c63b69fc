-- 主游戏逻辑 - 暗黑2风格游戏
-- 高性能Lua脚本，减少C#跨语言调用

-- 导入Unity和游戏模块
local CS = CS
local UnityEngine = CS.UnityEngine
local GameManager = CS.GameManager
local Vector3 = UnityEngine.Vector3
local Time = UnityEngine.Time

-- 游戏状态
local gameState = {
    isInitialized = false,
    player = nil,
    enemies = {},
    items = {},
    lastUpdateTime = 0,
    frameCount = 0
}

-- 性能优化：缓存常用函数
local log = CS.UnityEngine.Debug.Log
local logWarning = CS.UnityEngine.Debug.LogWarning
local logError = CS.UnityEngine.Debug.LogError

-- 批量操作缓存
local batchUpdateData = {}
local batchSpawnData = {}
local batchDestroyList = {}

-- 事件处理器
local eventHandlers = {}

-- ==================== 生命周期函数 ====================

function Start()
    log("Lua: 游戏开始初始化")
    
    -- 初始化游戏系统
    InitializeGameSystems()
    
    -- 创建玩家
    CreatePlayer()
    
    -- 生成初始敌人
    SpawnInitialEnemies()
    
    gameState.isInitialized = true
    log("Lua: 游戏初始化完成")
end

function Update(deltaTime)
    if not gameState.isInitialized then
        return
    end
    
    gameState.frameCount = gameState.frameCount + 1
    gameState.lastUpdateTime = Time.time
    
    -- 处理输入
    HandleInput()
    
    -- 更新游戏逻辑
    UpdateGameLogic(deltaTime)
    
    -- 批量更新实体 (每5帧执行一次，减少调用频率)
    if gameState.frameCount % 5 == 0 then
        BatchUpdateEntities()
    end
    
    -- 清理无效实体 (每60帧执行一次)
    if gameState.frameCount % 60 == 0 then
        CleanupInvalidEntities()
    end
end

function FixedUpdate(fixedDeltaTime)
    -- 物理相关更新
    UpdatePhysics(fixedDeltaTime)
end

function OnDestroy()
    log("Lua: 游戏销毁")
    -- 清理资源
    CleanupResources()
end

-- ==================== 初始化系统 ====================

function InitializeGameSystems()
    -- 注册事件处理器
    RegisterEventHandler("PlayerDeath", OnPlayerDeath)
    RegisterEventHandler("EnemyDeath", OnEnemyDeath)
    RegisterEventHandler("ItemPickup", OnItemPickup)
    RegisterEventHandler("LevelUp", OnLevelUp)
    
    -- 初始化游戏数据
    gameState.enemies = {}
    gameState.items = {}
    
    log("Lua: 游戏系统初始化完成")
end

function CreatePlayer()
    local playerStats = CS.GameStats()
    playerStats.health = 100
    playerStats.maxHealth = 100
    playerStats.mana = 50
    playerStats.maxMana = 50
    playerStats.attack = 10
    playerStats.defense = 5
    playerStats.speed = 5.0
    playerStats.level = 1
    playerStats.experience = 0
    
    local playerPos = Vector3(0, 0, 0)
    
    -- 这里应该调用EntityManager创建实体
    -- gameState.player = EntityManager.CreateEntity(EntityType.Player, playerPos, playerStats)
    
    log("Lua: 玩家创建完成")
end

function SpawnInitialEnemies()
    local enemyCount = 10
    local spawnRadius = 20.0
    
    for i = 1, enemyCount do
        local angle = (i / enemyCount) * 2 * math.pi
        local x = math.cos(angle) * spawnRadius
        local z = math.sin(angle) * spawnRadius
        local position = Vector3(x, 0, z)
        
        SpawnEnemy(position)
    end
    
    log("Lua: 初始敌人生成完成，数量: " .. enemyCount)
end

-- ==================== 游戏逻辑更新 ====================

function UpdateGameLogic(deltaTime)
    -- 更新玩家
    UpdatePlayer(deltaTime)
    
    -- 更新敌人AI
    UpdateEnemies(deltaTime)
    
    -- 更新物品
    UpdateItems(deltaTime)
    
    -- 检查碰撞
    CheckCollisions()
end

function UpdatePlayer(deltaTime)
    if not gameState.player then return end
    
    -- 玩家逻辑更新
    -- 这里可以添加玩家移动、技能冷却等逻辑
end

function UpdateEnemies(deltaTime)
    for enemyId, enemy in pairs(gameState.enemies) do
        -- 敌人AI逻辑
        UpdateEnemyAI(enemyId, enemy, deltaTime)
    end
end

function UpdateEnemyAI(enemyId, enemy, deltaTime)
    -- 简单的追击AI
    if gameState.player then
        local playerPos = Vector3(0, 0, 0) -- 应该从EntityManager获取玩家位置
        local enemyPos = enemy.position
        
        -- 计算移动方向
        local direction = (playerPos - enemyPos).normalized
        local moveSpeed = 2.0
        
        -- 更新敌人位置
        enemy.position = enemyPos + direction * moveSpeed * deltaTime
        
        -- 添加到批量更新列表
        AddToBatchUpdate(enemyId, enemy.position, enemy.stats)
    end
end

function UpdateItems(deltaTime)
    for itemId, item in pairs(gameState.items) do
        -- 物品逻辑更新（如闪烁效果等）
    end
end

function UpdatePhysics(fixedDeltaTime)
    -- 物理更新逻辑
end

-- ==================== 批量操作优化 ====================

function AddToBatchUpdate(entityId, position, stats)
    batchUpdateData[entityId] = {
        position = position,
        stats = stats
    }
end

function BatchUpdateEntities()
    if next(batchUpdateData) == nil then
        return -- 没有数据需要更新
    end
    
    -- 调用C#批量更新接口
    -- GameManager.BatchUpdateEntities(batchUpdateData)
    
    -- 清空批量数据
    batchUpdateData = {}
end

function CleanupInvalidEntities()
    -- 清理死亡的敌人
    for enemyId, enemy in pairs(gameState.enemies) do
        if enemy.stats.health <= 0 then
            table.insert(batchDestroyList, enemyId)
            gameState.enemies[enemyId] = nil
        end
    end
    
    -- 批量销毁
    if #batchDestroyList > 0 then
        -- GameManager.BatchDestroyEntities(batchDestroyList)
        batchDestroyList = {}
    end
end

-- ==================== 实体生成 ====================

function SpawnEnemy(position)
    local enemyStats = CS.GameStats()
    enemyStats.health = 50
    enemyStats.maxHealth = 50
    enemyStats.attack = 8
    enemyStats.defense = 3
    enemyStats.speed = 3.0
    enemyStats.level = 1
    
    local enemy = {
        position = position,
        stats = enemyStats,
        aiState = "patrol",
        lastAttackTime = 0
    }
    
    -- 这里应该调用EntityManager创建实体
    local enemyId = math.random(1000, 9999) -- 临时ID
    gameState.enemies[enemyId] = enemy
    
    return enemyId
end

function SpawnItem(position, itemType)
    local item = {
        position = position,
        itemType = itemType,
        spawnTime = Time.time
    }
    
    local itemId = math.random(10000, 99999) -- 临时ID
    gameState.items[itemId] = item
    
    return itemId
end

-- ==================== 输入处理 ====================

function HandleInput()
    -- 处理玩家输入
    -- 这里可以添加移动、攻击、技能释放等输入处理
    
    -- 示例：WASD移动
    local moveVector = Vector3.zero
    
    if UnityEngine.Input.GetKey(UnityEngine.KeyCode.W) then
        moveVector = moveVector + Vector3.forward
    end
    if UnityEngine.Input.GetKey(UnityEngine.KeyCode.S) then
        moveVector = moveVector + Vector3.back
    end
    if UnityEngine.Input.GetKey(UnityEngine.KeyCode.A) then
        moveVector = moveVector + Vector3.left
    end
    if UnityEngine.Input.GetKey(UnityEngine.KeyCode.D) then
        moveVector = moveVector + Vector3.right
    end
    
    -- 应用移动
    if moveVector.magnitude > 0 then
        MovePlayer(moveVector.normalized)
    end
    
    -- 攻击输入
    if UnityEngine.Input.GetMouseButtonDown(0) then
        PlayerAttack()
    end
end

function MovePlayer(direction)
    if not gameState.player then return end
    
    local moveSpeed = 5.0
    local deltaTime = Time.deltaTime
    
    -- 更新玩家位置
    -- 这里应该通过EntityManager更新
end

function PlayerAttack()
    -- 玩家攻击逻辑
    log("Lua: 玩家攻击")
end

-- ==================== 碰撞检测 ====================

function CheckCollisions()
    -- 简化的碰撞检测
    -- 在实际项目中应该使用更高效的空间分割算法
end

-- ==================== 事件处理 ====================

function RegisterEventHandler(eventName, handler)
    if not eventHandlers[eventName] then
        eventHandlers[eventName] = {}
    end
    table.insert(eventHandlers[eventName], handler)
end

function OnGameEvent(eventName, args)
    if eventHandlers[eventName] then
        for _, handler in ipairs(eventHandlers[eventName]) do
            handler(args)
        end
    end
end

function OnPlayerDeath(args)
    log("Lua: 玩家死亡")
    -- 处理玩家死亡逻辑
end

function OnEnemyDeath(args)
    local enemyId = args[1]
    log("Lua: 敌人死亡 ID: " .. tostring(enemyId))
    
    -- 移除敌人
    gameState.enemies[enemyId] = nil
    
    -- 可能掉落物品
    if math.random() < 0.3 then -- 30%概率掉落
        local enemy = gameState.enemies[enemyId]
        if enemy then
            SpawnItem(enemy.position, "gold")
        end
    end
end

function OnItemPickup(args)
    local itemId = args[1]
    local playerId = args[2]
    
    log("Lua: 物品拾取 ItemID: " .. tostring(itemId))
    
    -- 移除物品
    gameState.items[itemId] = nil
end

function OnLevelUp(args)
    local playerId = args[1]
    local newLevel = args[2]
    
    log("Lua: 玩家升级到 " .. tostring(newLevel) .. " 级")
end

-- ==================== 工具函数 ====================

function CleanupResources()
    gameState.enemies = {}
    gameState.items = {}
    batchUpdateData = {}
    batchSpawnData = {}
    batchDestroyList = {}
end

-- ==================== 调试函数 ====================

function GetGameState()
    return gameState
end

function GetEnemyCount()
    local count = 0
    for _ in pairs(gameState.enemies) do
        count = count + 1
    end
    return count
end

function GetItemCount()
    local count = 0
    for _ in pairs(gameState.items) do
        count = count + 1
    end
    return count
end

-- 导出函数供C#调用
_G.Start = Start
_G.Update = Update
_G.FixedUpdate = FixedUpdate
_G.OnDestroy = OnDestroy
_G.OnGameEvent = OnGameEvent
_G.SpawnEntity = SpawnEnemy
_G.DestroyEntity = function(entityId)
    gameState.enemies[entityId] = nil
    gameState.items[entityId] = nil
end

log("Lua: main.lua 加载完成")
