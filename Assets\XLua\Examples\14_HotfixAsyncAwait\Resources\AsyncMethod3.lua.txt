﻿require 'await'
local Task = CS.System.Threading.Tasks.Task

local function AsyncMethod3(self)
    CS.UnityEngine.Debug.Log("AsyncMethod3 in lua!    001")
    await(Task.Delay(1000), function ()
        CS.UnityEngine.Debug.Log("AsyncMethod3 in lua!    002")
        await(self:MyTask(), function ()
            CS.UnityEngine.Debug.Log("AsyncMethod3 in lua!    003")
        end)
    end)
end

xlua.hotfix(CS.XLuaTest.HotfixAsyncAwaitTest, 'AsyncMethod3', AsyncMethod3)


