using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;

/// <summary>
/// 游戏管理器 - 高性能Lua-C#桥接层
/// 专为暗黑2风格游戏设计，减少跨语言调用开销
/// </summary>
public class GameManager : MonoBehaviour
{
    #region 单例模式
    private static GameManager _instance;
    public static GameManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<GameManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("GameManager");
                    _instance = go.AddComponent<GameManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }
    #endregion

    #region XLua环境
    private LuaEnv luaEnv;
    private LuaTable scriptEnv;
    
    // 缓存的Lua函数委托 - 避免重复获取
    private Action luaStart;
    private Action<float> luaUpdate;
    private Action<float> luaFixedUpdate;
    private Action luaOnDestroy;
    
    // 游戏系统委托
    private Action<string, object[]> luaOnGameEvent;
    private Func<int, Vector3, bool> luaSpawnEntity;
    private Action<int> luaDestroyEntity;
    private Func<int, int, bool> luaCanAttack;
    #endregion

    #region 批量操作缓存
    // 批量数据传输，减少跨语言调用次数
    private List<int> batchEntityIds = new List<int>();
    private List<Vector3> batchPositions = new List<Vector3>();
    private List<GameStats> batchStats = new List<GameStats>();
    private List<ItemData> batchItems = new List<ItemData>();
    
    // 事件队列 - 批量处理事件
    private Queue<GameEvent> eventQueue = new Queue<GameEvent>();
    private const int MAX_EVENTS_PER_FRAME = 50;
    #endregion

    #region 游戏状态
    [Header("游戏配置")]
    public bool enableLuaHotReload = true;
    public float luaGCInterval = 1.0f;
    
    private float lastGCTime;
    private bool isInitialized = false;
    #endregion

    #region Unity生命周期
    void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeLua();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        if (isInitialized)
        {
            luaStart?.Invoke();
        }
    }

    void Update()
    {
        if (!isInitialized) return;

        float deltaTime = Time.deltaTime;
        
        // 处理事件队列
        ProcessEventQueue();
        
        // 调用Lua更新
        luaUpdate?.Invoke(deltaTime);
        
        // 定期GC
        if (Time.time - lastGCTime > luaGCInterval)
        {
            luaEnv?.Tick();
            lastGCTime = Time.time;
        }
    }

    void FixedUpdate()
    {
        if (isInitialized)
        {
            luaFixedUpdate?.Invoke(Time.fixedDeltaTime);
        }
    }

    void OnDestroy()
    {
        luaOnDestroy?.Invoke();
        luaEnv?.Dispose();
    }
    #endregion

    #region Lua初始化
    private void InitializeLua()
    {
        try
        {
            // 创建Lua环境
            luaEnv = new LuaEnv();
            
            // 设置Lua搜索路径
            luaEnv.DoString(@"
                package.path = package.path .. ';' .. CS.UnityEngine.Application.dataPath .. '/Lua/?.lua'
                package.path = package.path .. ';' .. CS.UnityEngine.Application.streamingAssetsPath .. '/Lua/?.lua'
            ");
            
            // 注册C#接口到Lua
            RegisterCSharpAPIs();
            
            // 加载主Lua脚本
            LoadMainLuaScript();
            
            // 缓存Lua函数
            CacheLuaFunctions();
            
            isInitialized = true;
            Debug.Log("GameManager: Lua环境初始化完成");
        }
        catch (Exception e)
        {
            Debug.LogError($"GameManager: Lua初始化失败 - {e.Message}");
        }
    }

    private void RegisterCSharpAPIs()
    {
        // 注册游戏管理器到Lua全局变量
        luaEnv.Global.Set("GameManager", this);
        
        // 注册批量操作接口
        luaEnv.Global.Set("BatchUpdateEntities", new Action<LuaTable>(BatchUpdateEntities));
        luaEnv.Global.Set("BatchSpawnEntities", new Action<LuaTable>(BatchSpawnEntities));
        luaEnv.Global.Set("BatchDestroyEntities", new Action<LuaTable>(BatchDestroyEntities));
        
        // 注册事件系统
        luaEnv.Global.Set("PostGameEvent", new Action<string, object[]>(PostGameEvent));
        luaEnv.Global.Set("RegisterEventHandler", new Action<string, Action<object[]>>(RegisterEventHandler));
        
        // 注册工具函数
        luaEnv.Global.Set("GetDeltaTime", new Func<float>(() => Time.deltaTime));
        luaEnv.Global.Set("GetTime", new Func<float>(() => Time.time));
        luaEnv.Global.Set("LogInfo", new Action<string>(Debug.Log));
        luaEnv.Global.Set("LogWarning", new Action<string>(Debug.LogWarning));
        luaEnv.Global.Set("LogError", new Action<string>(Debug.LogError));
    }

    private void LoadMainLuaScript()
    {
        // 尝试加载主游戏脚本
        string[] possiblePaths = {
            "Assets/Lua/main.lua",
            "Assets/StreamingAssets/Lua/main.lua",
            "Lua/main"
        };

        foreach (string path in possiblePaths)
        {
            try
            {
                if (path.EndsWith(".lua"))
                {
                    TextAsset luaScript = Resources.Load<TextAsset>(path.Replace("Assets/Resources/", "").Replace(".lua", ""));
                    if (luaScript != null)
                    {
                        luaEnv.DoString(luaScript.text, "main.lua");
                        Debug.Log($"GameManager: 成功加载Lua脚本 - {path}");
                        return;
                    }
                }
                else
                {
                    luaEnv.DoString($"require '{path}'");
                    Debug.Log($"GameManager: 成功require Lua模块 - {path}");
                    return;
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"GameManager: 无法加载Lua脚本 {path} - {e.Message}");
            }
        }
        
        Debug.LogWarning("GameManager: 未找到主Lua脚本，将使用默认配置");
    }

    private void CacheLuaFunctions()
    {
        try
        {
            // 缓存生命周期函数
            luaStart = luaEnv.Global.Get<Action>("Start");
            luaUpdate = luaEnv.Global.Get<Action<float>>("Update");
            luaFixedUpdate = luaEnv.Global.Get<Action<float>>("FixedUpdate");
            luaOnDestroy = luaEnv.Global.Get<Action>("OnDestroy");
            
            // 缓存游戏系统函数
            luaOnGameEvent = luaEnv.Global.Get<Action<string, object[]>>("OnGameEvent");
            luaSpawnEntity = luaEnv.Global.Get<Func<int, Vector3, bool>>("SpawnEntity");
            luaDestroyEntity = luaEnv.Global.Get<Action<int>>("DestroyEntity");
            luaCanAttack = luaEnv.Global.Get<Func<int, int, bool>>("CanAttack");
            
            Debug.Log("GameManager: Lua函数缓存完成");
        }
        catch (Exception e)
        {
            Debug.LogWarning($"GameManager: Lua函数缓存部分失败 - {e.Message}");
        }
    }
    #endregion

    #region 批量操作接口
    /// <summary>
    /// 批量更新实体 - 减少跨语言调用
    /// </summary>
    public void BatchUpdateEntities(LuaTable entityData)
    {
        batchEntityIds.Clear();
        batchPositions.Clear();
        batchStats.Clear();
        
        // 从Lua表中提取数据
        entityData.ForEach<int, LuaTable>((id, data) =>
        {
            batchEntityIds.Add(id);
            batchPositions.Add(data.Get<Vector3>("position"));
            batchStats.Add(data.Get<GameStats>("stats"));
        });
        
        // 批量处理实体更新
        for (int i = 0; i < batchEntityIds.Count; i++)
        {
            UpdateEntityInternal(batchEntityIds[i], batchPositions[i], batchStats[i]);
        }
    }

    /// <summary>
    /// 批量生成实体
    /// </summary>
    public void BatchSpawnEntities(LuaTable spawnData)
    {
        spawnData.ForEach<int, LuaTable>((index, data) =>
        {
            int entityId = data.Get<int>("id");
            Vector3 position = data.Get<Vector3>("position");
            SpawnEntityInternal(entityId, position);
        });
    }

    /// <summary>
    /// 批量销毁实体
    /// </summary>
    public void BatchDestroyEntities(LuaTable entityIds)
    {
        entityIds.ForEach<int, int>((index, entityId) =>
        {
            DestroyEntityInternal(entityId);
        });
    }
    #endregion

    #region 事件系统
    private Dictionary<string, List<Action<object[]>>> eventHandlers = new Dictionary<string, List<Action<object[]>>>();

    public void PostGameEvent(string eventName, params object[] args)
    {
        eventQueue.Enqueue(new GameEvent { name = eventName, args = args });
    }

    public void RegisterEventHandler(string eventName, Action<object[]> handler)
    {
        if (!eventHandlers.ContainsKey(eventName))
        {
            eventHandlers[eventName] = new List<Action<object[]>>();
        }
        eventHandlers[eventName].Add(handler);
    }

    private void ProcessEventQueue()
    {
        int processedCount = 0;
        while (eventQueue.Count > 0 && processedCount < MAX_EVENTS_PER_FRAME)
        {
            GameEvent gameEvent = eventQueue.Dequeue();
            
            if (eventHandlers.ContainsKey(gameEvent.name))
            {
                foreach (var handler in eventHandlers[gameEvent.name])
                {
                    try
                    {
                        handler?.Invoke(gameEvent.args);
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"GameManager: 事件处理器异常 {gameEvent.name} - {e.Message}");
                    }
                }
            }
            
            // 同时通知Lua
            luaOnGameEvent?.Invoke(gameEvent.name, gameEvent.args);
            
            processedCount++;
        }
    }
    #endregion

    #region 内部实现
    private void UpdateEntityInternal(int entityId, Vector3 position, GameStats stats)
    {
        // 实际的实体更新逻辑
        // 这里可以调用具体的游戏系统
    }

    private void SpawnEntityInternal(int entityId, Vector3 position)
    {
        // 实际的实体生成逻辑
    }

    private void DestroyEntityInternal(int entityId)
    {
        // 实际的实体销毁逻辑
    }
    #endregion

    #region 工具方法
    /// <summary>
    /// 热重载Lua脚本 (开发时使用)
    /// </summary>
    [ContextMenu("重载Lua脚本")]
    public void ReloadLuaScript()
    {
        if (enableLuaHotReload && Application.isEditor)
        {
            try
            {
                LoadMainLuaScript();
                CacheLuaFunctions();
                Debug.Log("GameManager: Lua脚本热重载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"GameManager: Lua脚本热重载失败 - {e.Message}");
            }
        }
    }

    /// <summary>
    /// 执行Lua代码
    /// </summary>
    public object DoLuaString(string luaCode)
    {
        try
        {
            return luaEnv.DoString(luaCode);
        }
        catch (Exception e)
        {
            Debug.LogError($"GameManager: Lua代码执行失败 - {e.Message}");
            return null;
        }
    }
    #endregion
}

/// <summary>
/// 游戏事件结构
/// </summary>
public struct GameEvent
{
    public string name;
    public object[] args;
}
