-- <PERSON><PERSON> is pleased to support the open source community by making xLua available.
-- Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
-- Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
-- http://opensource.org/licenses/MIT
-- Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.

local cs_coroutine = (require 'cs_coroutine')

local a = cs_coroutine.start(function()
    print('coroutine a started')

	coroutine.yield(cs_coroutine.start(function() 
		print('coroutine b stated inside cotoutine a')
		coroutine.yield(CS.UnityEngine.WaitForSeconds(1))
		print('i am coroutine b')
	end))
	print('coroutine b finish')

	while true do
		coroutine.yield(CS.UnityEngine.WaitForSeconds(1))
		print('i am coroutine a')
	end
end)

cs_coroutine.start(function()
    print('stop coroutine a after 5 seconds')
	coroutine.yield(CS.UnityEngine.WaitForSeconds(5))
	cs_coroutine.stop(a)
    print('coroutine a stoped')
end)


