Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.15f1 (faa32412f6c9) revision 16425764'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22621) 64bit ProfessionalWorkstation' Language: 'zh' Physical Memory: 65137 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-13T09:51:12Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
F:/Unity/Unity6/My project
-logFile
Logs/AssetImportWorker2.log
-srvPort
52194
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/Unity/Unity6/My project
F:/Unity/Unity6/My project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [53996]  Target information:

Player connection [53996]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 4148157423 [EditorId] 4148157423 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-2DTR2RN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [53996]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4148157423 [EditorId] 4148157423 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-2DTR2RN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [53996]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4148157423 [EditorId] 4148157423 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-2DTR2RN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [53996] Host joined multi-casting on [***********:54997]...
Player connection [53996] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 8.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.15f1 (faa32412f6c9)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Unity/Unity6/My project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 3070 (ID=0x2484)
    Vendor:          NVIDIA
    VRAM:            8018 MB
    App VRAM Budget: 7250 MB
    Driver:          32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56980
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.008633 seconds.
- Loaded All Assemblies, in  0.487 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.395 seconds
Domain Reload Profiling: 878ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (82ms)
	LoadAllAssembliesAndSetupDomain (184ms)
		LoadAssemblies (155ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (179ms)
				TypeCache.ScanAssembly (163ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (395ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (345ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (61ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (140ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.972 seconds
Refreshing native plugins compatible for Editor in 4.09 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.949 seconds
Domain Reload Profiling: 1917ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (631ms)
		LoadAssemblies (465ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (291ms)
			TypeCache.Refresh (217ms)
				TypeCache.ScanAssembly (193ms)
			BuildScriptInfoCaches (57ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (950ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (190ms)
			ProcessInitializeOnLoadAttributes (444ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 3.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6207 unused Assets / (6.3 MB). Loaded Objects now: 6832.
Memory consumption went from 142.6 MB to 136.3 MB.
Total: 9.240200 ms (FindLiveObjects: 0.986600 ms CreateObjectMapping: 0.715600 ms MarkObjects: 4.669000 ms  DeleteObjects: 2.867700 ms)

========================================================================
Received Import Request.
  Time since last request: 198609.047492 seconds.
  path: Assets/TutorialInfo
  artifactKey: Guid(ba062aa6c92b140379dbc06b43dd3b9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TutorialInfo using Guid(ba062aa6c92b140379dbc06b43dd3b9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd0ce3b9fb2b0078fcd59af112aef3b6') in 0.0086254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.977121 seconds.
  path: Assets/TutorialInfo/Layout.wlt
  artifactKey: Guid(eabc9546105bf4accac1fd62a63e88e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TutorialInfo/Layout.wlt using Guid(eabc9546105bf4accac1fd62a63e88e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '649dfc59f4f805dd3e0f71c935031ed6') in 0.0202855 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 4.132398 seconds.
  path: Assets/TutorialInfo/Scripts
  artifactKey: Guid(5a9bcd70e6a4b4b05badaa72e827d8e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TutorialInfo/Scripts using Guid(5a9bcd70e6a4b4b05badaa72e827d8e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd5f7b6d5fe4d6ec12174dd17021b0f3b') in 0.0005407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.190589 seconds.
  path: Assets/TutorialInfo/Scripts/Readme.cs
  artifactKey: Guid(fcf7219bab7fe46a1ad266029b2fee19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TutorialInfo/Scripts/Readme.cs using Guid(fcf7219bab7fe46a1ad266029b2fee19) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c344305f2ca51ffd332a0fa4ea5f624') in 0.0007568 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.979728 seconds.
  path: Assets/TutorialInfo/Scripts/Editor
  artifactKey: Guid(3ad9b87dffba344c89909c6d1b1c17e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TutorialInfo/Scripts/Editor using Guid(3ad9b87dffba344c89909c6d1b1c17e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2befc4d32e7a4c91df83b6e6adefcc2a') in 0.0006217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.553785 seconds.
  path: Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs
  artifactKey: Guid(476cc7d7cd9874016adc216baab94a0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs using Guid(476cc7d7cd9874016adc216baab94a0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61e56ed46c2d4246d2dc42ddb4eb712b') in 0.0014155 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.429 seconds
Refreshing native plugins compatible for Editor in 4.14 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.251 seconds
Domain Reload Profiling: 2679ms
	BeginReloadAssembly (478ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (809ms)
		LoadAssemblies (777ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (325ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (287ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1252ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (943ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (254ms)
			ProcessInitializeOnLoadAttributes (570ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 3.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6204 unused Assets / (6.4 MB). Loaded Objects now: 6851.
Memory consumption went from 149.9 MB to 143.5 MB.
Total: 9.352000 ms (FindLiveObjects: 0.749100 ms CreateObjectMapping: 0.585500 ms MarkObjects: 4.935000 ms  DeleteObjects: 3.081000 ms)

Prepare: number of updated asset objects reloaded= 5
========================================================================
Received Import Request.
  Time since last request: 51.769130 seconds.
  path: Assets/Editor/com.unity.mobile.notifications
  artifactKey: Guid(6d8e2e3178665be488e481c57be9527e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Editor/com.unity.mobile.notifications using Guid(6d8e2e3178665be488e481c57be9527e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f366f037cbda33fd8f5351159b2a360') in 0.0018822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.015925 seconds.
  path: Assets/Editor/com.unity.mobile.notifications/NotificationSettings.asset
  artifactKey: Guid(45a04f37e0f48c744acc0874c4a8918a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Editor/com.unity.mobile.notifications/NotificationSettings.asset using Guid(45a04f37e0f48c744acc0874c4a8918a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f20f792884be25fd377bf0a7a52e9dfc') in 0.027534 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 23.511947 seconds.
  path: Assets/Settings/SampleSceneProfile.asset
  artifactKey: Guid(10fc4df2da32a41aaa32d77bc913491c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/SampleSceneProfile.asset using Guid(10fc4df2da32a41aaa32d77bc913491c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ee95093a9b44ac9f36a77967a816896') in 0.0034073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.062 seconds
Refreshing native plugins compatible for Editor in 3.26 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.049 seconds
Domain Reload Profiling: 2109ms
	BeginReloadAssembly (309ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (629ms)
		LoadAssemblies (560ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (191ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1050ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (850ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (290ms)
			ProcessInitializeOnLoadAttributes (462ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6204 unused Assets / (6.5 MB). Loaded Objects now: 6868.
Memory consumption went from 150.0 MB to 143.5 MB.
Total: 9.252800 ms (FindLiveObjects: 0.716200 ms CreateObjectMapping: 0.626700 ms MarkObjects: 4.616400 ms  DeleteObjects: 3.292500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.069 seconds
Refreshing native plugins compatible for Editor in 4.53 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.081 seconds
Domain Reload Profiling: 2150ms
	BeginReloadAssembly (310ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (645ms)
		LoadAssemblies (557ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (246ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (220ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1081ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (855ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (278ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 3.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6204 unused Assets / (6.0 MB). Loaded Objects now: 6870.
Memory consumption went from 151.5 MB to 145.5 MB.
Total: 8.798600 ms (FindLiveObjects: 0.721800 ms CreateObjectMapping: 0.625200 ms MarkObjects: 4.269300 ms  DeleteObjects: 3.181400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6186 unused Assets / (7.8 MB). Loaded Objects now: 6871.
Memory consumption went from 153.1 MB to 145.3 MB.
Total: 17.256200 ms (FindLiveObjects: 0.855900 ms CreateObjectMapping: 0.854000 ms MarkObjects: 6.970300 ms  DeleteObjects: 8.574000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.010 seconds
Refreshing native plugins compatible for Editor in 4.09 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.061 seconds
Domain Reload Profiling: 2072ms
	BeginReloadAssembly (296ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (532ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (223ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1064ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (844ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (256ms)
			ProcessInitializeOnLoadAttributes (490ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (17ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 5.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6205 unused Assets / (6.1 MB). Loaded Objects now: 6873.
Memory consumption went from 154.5 MB to 148.4 MB.
Total: 11.727400 ms (FindLiveObjects: 1.705000 ms CreateObjectMapping: 1.037200 ms MarkObjects: 4.635800 ms  DeleteObjects: 4.346400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 233.583822 seconds.
  path: Assets/Script/Main.cs
  artifactKey: Guid(57229493ac4a0444fa99d5ab9c05aab9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Main.cs using Guid(57229493ac4a0444fa99d5ab9c05aab9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da46480a58634e39f7412767c00a8614') in 0.0032047 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 333.968730 seconds.
  path: Assets/Script
  artifactKey: Guid(caba01fe9ef095043993d3851b9545cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script using Guid(caba01fe9ef095043993d3851b9545cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f57da866526c9157ae2e0c20249c6e3') in 0.0010238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.769 seconds
Refreshing native plugins compatible for Editor in 10.12 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.122 seconds
Domain Reload Profiling: 4896ms
	BeginReloadAssembly (596ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (191ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (2916ms)
		LoadAssemblies (3067ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (241ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1123ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (879ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (286ms)
			ProcessInitializeOnLoadAttributes (484ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 6.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (7.1 MB). Loaded Objects now: 6930.
Memory consumption went from 151.1 MB to 144.1 MB.
Total: 18.247500 ms (FindLiveObjects: 0.939100 ms CreateObjectMapping: 0.815900 ms MarkObjects: 6.619600 ms  DeleteObjects: 9.870300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 700.751402 seconds.
  path: Assets/XLua/Examples
  artifactKey: Guid(1feab61bf70ce6a4d951cbf76ea6c32f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples using Guid(1feab61bf70ce6a4d951cbf76ea6c32f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd710eb0d468f40a6f370e01abcfec91') in 0.003839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.297459 seconds.
  path: Assets/XLua/Examples/01_Helloworld
  artifactKey: Guid(c8fbb1a01533805479599cd3e19a5e4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/01_Helloworld using Guid(c8fbb1a01533805479599cd3e19a5e4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0222e3c104f4bb56ff54103e2411c8fb') in 0.0006403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.387566 seconds.
  path: Assets/XLua/Examples/01_Helloworld/Helloworld.unity
  artifactKey: Guid(58e2eda480b0c6e41a793391a9fb9a72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/01_Helloworld/Helloworld.unity using Guid(58e2eda480b0c6e41a793391a9fb9a72) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '86a78a055232ee267816fcb092708d3c') in 0.0006068 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.022 seconds
Refreshing native plugins compatible for Editor in 7.58 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.060 seconds
Domain Reload Profiling: 2082ms
	BeginReloadAssembly (295ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (632ms)
		LoadAssemblies (525ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (240ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (212ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1060ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (820ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (16ms)
			BeforeProcessingInitializeOnLoad (223ms)
			ProcessInitializeOnLoadAttributes (494ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (19ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 10.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.8 MB). Loaded Objects now: 6932.
Memory consumption went from 151.9 MB to 145.1 MB.
Total: 14.340900 ms (FindLiveObjects: 1.021800 ms CreateObjectMapping: 0.904900 ms MarkObjects: 7.726200 ms  DeleteObjects: 4.686200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 26.162718 seconds.
  path: Assets/XLua/Examples/01_Helloworld/HelloworldSettings.lighting
  artifactKey: Guid(316001411a844e548a225b6a9a0f15ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/01_Helloworld/HelloworldSettings.lighting using Guid(316001411a844e548a225b6a9a0f15ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '10d65e386728d94d0154e145e9fc36fc') in 0.0274339 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.133020 seconds.
  path: Assets/XLua/Examples/01_Helloworld/Helloworld.cs
  artifactKey: Guid(480b23bd4e378f2499f8b31ae84b1c05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/01_Helloworld/Helloworld.cs using Guid(480b23bd4e378f2499f8b31ae84b1c05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '78153b622f9149116d05a6320729107e') in 0.0008408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6932.
Memory consumption went from 147.3 MB to 144.3 MB.
Total: 11.247600 ms (FindLiveObjects: 0.458800 ms CreateObjectMapping: 0.371200 ms MarkObjects: 9.162800 ms  DeleteObjects: 1.252900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 14.113067 seconds.
  path: Assets/XLua/Examples/02_U3DScripting
  artifactKey: Guid(afd3fcbd8a9d48945b874adff2c0aa6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/02_U3DScripting using Guid(afd3fcbd8a9d48945b874adff2c0aa6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '912e2f4271b21013945b0619b0041ac2') in 0.0007556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.607402 seconds.
  path: Assets/XLua/Examples/02_U3DScripting/U3DScripting.unity
  artifactKey: Guid(6d9b7cd8ac592564796c9c24b3636677) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/02_U3DScripting/U3DScripting.unity using Guid(6d9b7cd8ac592564796c9c24b3636677) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '769ea5b7287a55a69924493fefde0c88') in 0.0005845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.032 seconds
Refreshing native plugins compatible for Editor in 10.49 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.179 seconds
Domain Reload Profiling: 2216ms
	BeginReloadAssembly (322ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (531ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (220ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (191ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1183ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (966ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (279ms)
			ProcessInitializeOnLoadAttributes (573ms)
			ProcessInitializeOnLoadMethodAttributes (84ms)
			AfterProcessingInitializeOnLoad (14ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 7.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.1 MB). Loaded Objects now: 6934.
Memory consumption went from 152.0 MB to 145.9 MB.
Total: 18.345600 ms (FindLiveObjects: 2.734100 ms CreateObjectMapping: 1.667600 ms MarkObjects: 7.539700 ms  DeleteObjects: 6.402400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 12.541942 seconds.
  path: Assets/XLua/Examples/02_U3DScripting/U3DScriptingSettings.lighting
  artifactKey: Guid(405a71facdf0c484596804262e99566f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/02_U3DScripting/U3DScriptingSettings.lighting using Guid(405a71facdf0c484596804262e99566f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '402a032d868aaf443f5aff086232fbc0') in 0.0281584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.142117 seconds.
  path: Assets/XLua/Examples/02_U3DScripting/LuaTestScript.lua.txt
  artifactKey: Guid(7cfed86298c2da543b693ffd8436367a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/02_U3DScripting/LuaTestScript.lua.txt using Guid(7cfed86298c2da543b693ffd8436367a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9c4605c5087aa2fedd12c8917a20c43') in 0.0013223 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 16.003676 seconds.
  path: Assets/XLua/Examples/02_U3DScripting/LuaBehaviour.cs
  artifactKey: Guid(b9173d38911503c4a9a371b0760bdd15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/02_U3DScripting/LuaBehaviour.cs using Guid(b9173d38911503c4a9a371b0760bdd15) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'caa6c3fab615c55c268df4cf7e8d0522') in 0.0006618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 6.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6934.
Memory consumption went from 147.5 MB to 144.4 MB.
Total: 7.420700 ms (FindLiveObjects: 0.433300 ms CreateObjectMapping: 0.380700 ms MarkObjects: 5.537700 ms  DeleteObjects: 1.067500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 17.976150 seconds.
  path: Assets/XLua/Examples/03_UIEvent/ButtonInteraction.lua.txt
  artifactKey: Guid(9b998ff8754d165438f1f34adb4f3d7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/03_UIEvent/ButtonInteraction.lua.txt using Guid(9b998ff8754d165438f1f34adb4f3d7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '250c57743706b5c6329aa0cf9746bc27') in 0.0061074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.466686 seconds.
  path: Assets/XLua/Examples/03_UIEvent/UISettings.lighting
  artifactKey: Guid(8fc65da551af76948a46eeffee7fc126) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/03_UIEvent/UISettings.lighting using Guid(8fc65da551af76948a46eeffee7fc126) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0bbb0c3b055bb366f3da97792c5dda84') in 0.0016944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.994890 seconds.
  path: Assets/XLua/Examples/03_UIEvent/UI.unity
  artifactKey: Guid(f835eeddd75e9f74d95c957aa7ca44b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/03_UIEvent/UI.unity using Guid(f835eeddd75e9f74d95c957aa7ca44b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15585375da7e695c1b5b6318cdcc12a4') in 0.0007545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.301 seconds
Refreshing native plugins compatible for Editor in 9.69 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.245 seconds
Domain Reload Profiling: 2548ms
	BeginReloadAssembly (361ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (814ms)
		LoadAssemblies (692ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (254ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1246ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1012ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (304ms)
			ProcessInitializeOnLoadAttributes (587ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 7.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.8 MB). Loaded Objects now: 6936.
Memory consumption went from 152.0 MB to 145.3 MB.
Total: 12.081300 ms (FindLiveObjects: 1.018200 ms CreateObjectMapping: 1.219700 ms MarkObjects: 5.264100 ms  DeleteObjects: 4.576300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 10.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6936.
Memory consumption went from 148.9 MB to 145.9 MB.
Total: 6.980500 ms (FindLiveObjects: 0.437700 ms CreateObjectMapping: 0.351500 ms MarkObjects: 5.124700 ms  DeleteObjects: 1.065200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.151 seconds
Refreshing native plugins compatible for Editor in 9.05 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.310 seconds
Domain Reload Profiling: 2460ms
	BeginReloadAssembly (325ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (684ms)
		LoadAssemblies (640ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (223ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1310ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1061ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (357ms)
			ProcessInitializeOnLoadAttributes (589ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 8.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (6.9 MB). Loaded Objects now: 6938.
Memory consumption went from 155.0 MB to 148.1 MB.
Total: 15.178600 ms (FindLiveObjects: 0.939400 ms CreateObjectMapping: 0.875200 ms MarkObjects: 8.646100 ms  DeleteObjects: 4.716500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6938.
Memory consumption went from 151.9 MB to 148.9 MB.
Total: 13.072700 ms (FindLiveObjects: 0.501000 ms CreateObjectMapping: 0.387800 ms MarkObjects: 9.770300 ms  DeleteObjects: 2.411100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.036 seconds
Refreshing native plugins compatible for Editor in 12.46 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.191 seconds
Domain Reload Profiling: 2231ms
	BeginReloadAssembly (291ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (656ms)
		LoadAssemblies (518ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (266ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (229ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1192ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (955ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (283ms)
			ProcessInitializeOnLoadAttributes (545ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 5.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (8.2 MB). Loaded Objects now: 6940.
Memory consumption went from 158.0 MB to 149.8 MB.
Total: 13.924600 ms (FindLiveObjects: 0.741300 ms CreateObjectMapping: 0.697700 ms MarkObjects: 6.290900 ms  DeleteObjects: 6.192800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (3.0 MB). Loaded Objects now: 6940.
Memory consumption went from 154.9 MB to 151.9 MB.
Total: 8.797800 ms (FindLiveObjects: 0.586500 ms CreateObjectMapping: 0.385300 ms MarkObjects: 6.491600 ms  DeleteObjects: 1.332300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 130.221146 seconds.
  path: Assets/XLua/Examples/04_LuaObjectOrented/InvokeLua.cs
  artifactKey: Guid(2e190b5ef59b7a84dbb262cfd11f9107) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/04_LuaObjectOrented/InvokeLua.cs using Guid(2e190b5ef59b7a84dbb262cfd11f9107) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b4caa914c9bd71e32c5cfa4917fccd44') in 0.0022059 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 5.835429 seconds.
  path: Assets/XLua/Examples/04_LuaObjectOrented/InvokeLuaSettings.lighting
  artifactKey: Guid(2d3042691d82ab64c94453c6a531b5ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/04_LuaObjectOrented/InvokeLuaSettings.lighting using Guid(2d3042691d82ab64c94453c6a531b5ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5021520ba47f84f8ec0c3d47a4f44704') in 0.0283514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.026732 seconds.
  path: Assets/XLua/Examples/04_LuaObjectOrented/InvokeLua.unity
  artifactKey: Guid(554d570d8b369434ba8a099ebafee542) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/04_LuaObjectOrented/InvokeLua.unity using Guid(554d570d8b369434ba8a099ebafee542) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04c1d9fd33fae9961c22fa041245edb0') in 0.0008029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 38.701035 seconds.
  path: Assets/XLua/Examples/09_GenericMethod/GenericMethod.unity
  artifactKey: Guid(19477d725133da049be476cb4caedf7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/09_GenericMethod/GenericMethod.unity using Guid(19477d725133da049be476cb4caedf7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ef731ba7eb3fe021e1c49491e5df7c53') in 0.0008003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 8.161835 seconds.
  path: Assets/XLua/Examples/09_GenericMethod/GenericMethod
  artifactKey: Guid(16ae28006c1e61140ac11a39c7a77dc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/09_GenericMethod/GenericMethod using Guid(16ae28006c1e61140ac11a39c7a77dc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a4ba3cf1afd618562ce017555805a18') in 0.0006364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.041230 seconds.
  path: Assets/XLua/Examples/09_GenericMethod/GenericMethod/LightingData.asset
  artifactKey: Guid(765a21d3da1f2fc42bf17c4d39611a9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/09_GenericMethod/GenericMethod/LightingData.asset using Guid(765a21d3da1f2fc42bf17c4d39611a9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9f36e43df3aa3422a025c01182f5ccd1') in 0.0017876 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 4.956532 seconds.
  path: Assets/XLua/Examples/09_GenericMethod/Foo.cs
  artifactKey: Guid(3894dbe73b280d44e9c58b8e33c93995) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/09_GenericMethod/Foo.cs using Guid(3894dbe73b280d44e9c58b8e33c93995) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f2ce14aab18d027b93f2b9785d21e5cf') in 0.0005764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 30.620670 seconds.
  path: Assets/XLua/Examples/09_GenericMethod/GenericMethodExample.cs
  artifactKey: Guid(ce77e35c083e6fe4c88cb64a97466161) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/09_GenericMethod/GenericMethodExample.cs using Guid(ce77e35c083e6fe4c88cb64a97466161) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '02774e41af3366ee79ecfb1e0583cfce') in 0.0006219 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.947302 seconds.
  path: Assets/XLua/Examples/09_GenericMethod/GenericMethodSettings.lighting
  artifactKey: Guid(8125d7d38daf23b4db955307405b0cf7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/09_GenericMethod/GenericMethodSettings.lighting using Guid(8125d7d38daf23b4db955307405b0cf7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8df5b51aea9a6fd8bbd29bb57214ee4d') in 0.0023696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 19.166800 seconds.
  path: Assets/XLua/Examples/13_BuildFromCLI/Editor
  artifactKey: Guid(266e4ca9a64f701449b0468b9a4f1d32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/13_BuildFromCLI/Editor using Guid(266e4ca9a64f701449b0468b9a4f1d32) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '397eea0c5814aa5848257ad4f7e8594c') in 0.0007101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.811444 seconds.
  path: Assets/XLua/Examples/13_BuildFromCLI/Editor/BuildFromCLI.cs
  artifactKey: Guid(1fd3fcc7509943c45af11f63df13d137) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/13_BuildFromCLI/Editor/BuildFromCLI.cs using Guid(1fd3fcc7509943c45af11f63df13d137) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58c17fa1921bc66700379c5e3a4fa1a7') in 0.0007623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 58.315968 seconds.
  path: Assets/XLua/Examples/ExampleGenConfig.cs
  artifactKey: Guid(62b967797782a6d46a759bf0693f7351) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Examples/ExampleGenConfig.cs using Guid(62b967797782a6d46a759bf0693f7351) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '225e66e6ca358a239454f2896436c9af') in 0.0007118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 23.445137 seconds.
  path: Assets/XLua/Src/Utils.cs
  artifactKey: Guid(d9c14a5b76adb7d41926526af904beda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Src/Utils.cs using Guid(d9c14a5b76adb7d41926526af904beda) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '64afbb36f42ddb00d0d597decc2097cb') in 0.0005795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 6.223558 seconds.
  path: Assets/XLua/Src/SignatureLoader.cs
  artifactKey: Guid(5dfa9c69dddc18849bd3c1dfc4ac42de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Src/SignatureLoader.cs using Guid(5dfa9c69dddc18849bd3c1dfc4ac42de) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5dc546f1fa3e1ecfb9dda440e1d15559') in 0.0007118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 8.370694 seconds.
  path: Assets/XLua/Resources
  artifactKey: Guid(fa4f7e825d6ae9742bd6f88af5865c13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Resources using Guid(fa4f7e825d6ae9742bd6f88af5865c13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed3f33f70fef97817af5e59fea4aedbe') in 0.0009429 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.275018 seconds.
  path: Assets/XLua/Resources/perf
  artifactKey: Guid(866368b69ae1a2040943783fa31d2f74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Resources/perf using Guid(866368b69ae1a2040943783fa31d2f74) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d9a2d742f35fc083f94ef00178448a6') in 0.0011637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.703727 seconds.
  path: Assets/XLua/Resources/perf/memory.lua.txt
  artifactKey: Guid(6a5cba5df35473342b614686c15f8a4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Resources/perf/memory.lua.txt using Guid(6a5cba5df35473342b614686c15f8a4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '81f6264a776b728fae0d37ffaa96234e') in 0.001351 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 5.928942 seconds.
  path: Assets/XLua/Resources/perf/profiler.lua.txt
  artifactKey: Guid(4841b87b13a684649aab9de0e72132b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Resources/perf/profiler.lua.txt using Guid(4841b87b13a684649aab9de0e72132b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '38fc8b858146780557b6249790f88fee') in 0.0013794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 12.960837 seconds.
  path: Assets/XLua/Resources/tdr
  artifactKey: Guid(8f08dfe3f4634334ea0810ea31d8b593) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Resources/tdr using Guid(8f08dfe3f4634334ea0810ea31d8b593) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50d41170bff8419c4297ef2760f2ebae') in 0.0015199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.040804 seconds.
  path: Assets/XLua/Resources/tdr/tdr.lua.txt
  artifactKey: Guid(9e05f05f4a331bc45a04832062650a9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XLua/Resources/tdr/tdr.lua.txt using Guid(9e05f05f4a331bc45a04832062650a9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dd31a9a6cf1f3fb13120dc2538b95fb1') in 0.0017007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 21.622986 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e20915ee4029c652b1162b68eec31d2') in 0.0006352 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.745 seconds
Refreshing native plugins compatible for Editor in 22.19 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.937 seconds
Domain Reload Profiling: 3679ms
	BeginReloadAssembly (431ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (108ms)
	RebuildCommonClasses (90ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (75ms)
	LoadAllAssembliesAndSetupDomain (1117ms)
		LoadAssemblies (844ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (468ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (412ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1939ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1465ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (431ms)
			ProcessInitializeOnLoadAttributes (729ms)
			ProcessInitializeOnLoadMethodAttributes (252ms)
			AfterProcessingInitializeOnLoad (36ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (53ms)
Refreshing native plugins compatible for Editor in 9.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6276 unused Assets / (7.0 MB). Loaded Objects now: 6942.
Memory consumption went from 152.1 MB to 145.1 MB.
Total: 17.439400 ms (FindLiveObjects: 0.909600 ms CreateObjectMapping: 2.034100 ms MarkObjects: 5.525600 ms  DeleteObjects: 8.968100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.907 seconds
Script error (EntityManager): Update() can not take parameters.
Refreshing native plugins compatible for Editor in 13.08 ms, found 4 plugins.
Script error (EntityManager): Update() can not take parameters.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.247 seconds
Domain Reload Profiling: 6155ms
	BeginReloadAssembly (794ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (127ms)
	RebuildCommonClasses (199ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (3845ms)
		LoadAssemblies (4002ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (375ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (328ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1248ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (997ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (290ms)
			ProcessInitializeOnLoadAttributes (602ms)
			ProcessInitializeOnLoadMethodAttributes (83ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 11.56 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6279 unused Assets / (5.9 MB). Loaded Objects now: 6947.
Memory consumption went from 153.6 MB to 147.7 MB.
Total: 18.620600 ms (FindLiveObjects: 1.123100 ms CreateObjectMapping: 1.563100 ms MarkObjects: 10.942000 ms  DeleteObjects: 4.989600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4025.328981 seconds.
  path: Assets/Script/PerformanceGuide.md
  artifactKey: Guid(7a70181e97badd44e96411e9cab1865f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/PerformanceGuide.md using Guid(7a70181e97badd44e96411e9cab1865f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ad85b403a87da6ca98ee6d76f087fe89') in 0.0285737 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 7.484436 seconds.
  path: Assets/Script/GameManager.cs
  artifactKey: Guid(d0c9c202a6c107d47a10dcbc676524b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/GameManager.cs using Guid(d0c9c202a6c107d47a10dcbc676524b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9dd7a562ca2478e1fa5f8dd1347d8b05') in 0.0006124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.489374 seconds.
  path: Assets/Script/GameConfig.cs
  artifactKey: Guid(3837bb93fbf79b94fb989fceedc6d6ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/GameConfig.cs using Guid(3837bb93fbf79b94fb989fceedc6d6ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ca85e88a4b988294f1e71cbf18adc10') in 0.0007168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.497321 seconds.
  path: Assets/Script/EntityManager.cs
  artifactKey: Guid(0d847a453dd75ef43b0cb6e5871cde8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/EntityManager.cs using Guid(0d847a453dd75ef43b0cb6e5871cde8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f3523bc7770d927797f6873090a58f81') in 0.0006044 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Script error (EntityManager): Update() can not take parameters.
Refreshing native plugins compatible for Editor in 16.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6244 unused Assets / (6.1 MB). Loaded Objects now: 6947.
Memory consumption went from 152.2 MB to 146.1 MB.
Total: 29.649800 ms (FindLiveObjects: 2.545600 ms CreateObjectMapping: 3.208300 ms MarkObjects: 15.375400 ms  DeleteObjects: 8.513500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.462 seconds
Script error (EntityManager): Update() can not take parameters.
Refreshing native plugins compatible for Editor in 8.02 ms, found 4 plugins.
Script error (EntityManager): Update() can not take parameters.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.213 seconds
Domain Reload Profiling: 2678ms
	BeginReloadAssembly (570ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (195ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (769ms)
		LoadAssemblies (735ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (272ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (234ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1213ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (938ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (241ms)
			ProcessInitializeOnLoadAttributes (582ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 8.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6279 unused Assets / (7.1 MB). Loaded Objects now: 6949.
Memory consumption went from 153.7 MB to 146.5 MB.
Total: 14.926500 ms (FindLiveObjects: 1.031100 ms CreateObjectMapping: 1.571300 ms MarkObjects: 5.328800 ms  DeleteObjects: 6.993200 ms)

Prepare: number of updated asset objects reloaded= 0
